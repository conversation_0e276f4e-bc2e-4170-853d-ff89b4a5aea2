"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { developmentSteps } from "@/lib/constant";
import CardWithHoverLighting from "../CardWithHoverlighting";
import Link from "next/link";
import { BlurFade } from "../magicui/blur-fade";

export const DevelopmentProcess = () => {
  return (
    <section className="bg-background py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <BlurFade delay={0.25} inView>
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl text-foreground mb-4">
              My Development Process
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              A structured approach that ensures your project is delivered on
              time, within budget, and exceeds your expectations.
            </p>
          </div>
        </BlurFade>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {developmentSteps.map((step, index) => {
            const IconComponent = step.icon;
            return (
              <BlurFade key={index} delay={0.25 + index * 0.05} inView>
                <CardWithHoverLighting>
                  <Card className="bg-transparent border-none p-0 py-5">
                    <CardHeader className="text-center pb-4">
                      <div className="mx-auto mb-4 p-4 bg-primary/5 rounded-full w-16 h-16 flex items-center justify-center group-hover:bg-primary/10 transition-colors duration-300">
                        <IconComponent className="w-8 h-8 text-primary" />
                      </div>
                      <div className="flex items-center justify-center gap-3 mb-2">
                        <span className="text-sm bg-primary text-primary-foreground px-3 py-1 rounded-full">
                          Step {step.id}
                        </span>
                      </div>
                      <CardTitle className="text-xl">{step.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-justify text-muted-foreground leading-relaxed">
                        {step.description}
                      </CardDescription>
                    </CardContent>
                  </Card>
                </CardWithHoverLighting>
              </BlurFade>
            );
          })}
        </div>
        <BlurFade delay={0.25 * 0.05} inView>
          <div className="text-center mt-16">
            <p className="text-muted-foreground mb-6">
              Ready to start your next project?
            </p>
            <Button
              size="lg"
              className="bg-primary text-primary-foreground hover:bg-primary/90 transition-all duration-300 hover:shadow-lg hover:shadow-primary/25"
            >
              <Link
                href="https://wa.me/923356478086"
                target="_blank"
                rel="noopener noreferrer"
              >
                Let's Discuss Your Project
              </Link>
            </Button>
          </div>
        </BlurFade>
      </div>
    </section>
  );
};
