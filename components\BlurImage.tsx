"use client"

import { useEffect, useState } from "react";

 const BlurImage = ({ src, alt }: { src: string; alt: string }) => {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const img = new Image();
    img.src = src;
    img.onload = () => {
      setIsLoaded(true);
    };
  }, [src]);

  return (
    <div
      className="w-full bg-gray-200 rounded-xl overflow-hidden shadow-lg"
      aria-live="polite"
      aria-busy={!isLoaded}
    >
      <img
        src={src}
        alt={alt}
        className={`w-full h-full object-cover transition-all duration-700 ease-out ${
          isLoaded ? "blur-0 scale-100" : "blur-2xl scale-110"
        }`}
        aria-hidden={!isLoaded}
        loading="lazy"
      />
    </div>
  );
};

export default BlurImage;