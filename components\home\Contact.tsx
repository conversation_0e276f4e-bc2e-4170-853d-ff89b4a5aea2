"use client";

import React, { useEffect, useRef } from "react";
import { useFormState, useFormStatus } from "react-dom";
import { toast } from "sonner";
import { sendEmail } from "@/app/actions/sendEmail";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Send, Loader2 } from "lucide-react";
import { BlurFade } from "../magicui/blur-fade";

const initialState = {
  success: false,
  name: "",
  email: "",
  message: "",
};

export const Contact = () => {
  const [state, formAction] = React.useActionState<any, FormData>(
    sendEmail,
    initialState
  );
  const formRef = useRef<HTMLFormElement>(null);

  useEffect(() => {
    if (state.success) {
      toast.success("Message sent successfully!");
      formRef.current?.reset();
    } else if (state.success === false && state.message) {
      toast.error(state.message);
    }
  }, [state]);

  return (
    <section id="contact" className="bg-muted/30 py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <BlurFade delay={0.25} inView>
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl text-foreground mb-4">
              Contact Me
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Ready to start your next project? Let's discuss how I can help
              bring your ideas to life.
            </p>
          </div>
        </BlurFade>

        <div className="max-w-2xl mx-auto">
          <BlurFade delay={0.25} inView>
            <Card className="border-border bg-card">
              <CardHeader>
                <CardTitle className="text-center">Get in Touch</CardTitle>
                <CardDescription className="text-center">
                  Fill out the form below and I'll get back to you within 24
                  hours.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form ref={formRef} action={formAction} className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      name="name"
                      type="text"
                      placeholder="Your full name"
                      required
                      className="bg-input-background border-border focus:ring-primary"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="<EMAIL>"
                      required
                      className="bg-input-background border-border focus:ring-primary"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="message">Message</Label>
                    <Textarea
                      id="message"
                      name="message"
                      placeholder="Tell me about your project, timeline, and any specific requirements..."
                      rows={6}
                      required
                      className="bg-input-background border-border focus:ring-primary resize-none"
                    />
                  </div>

                  <div className="flex flex-col sm:flex-row gap-4 pt-4">
                    <SubmitButton />
                    {/* <Button
                    type="button"
                    variant="outline"
                    size="lg"
                    className="flex-1 border-border hover:bg-accent hover:text-accent-foreground transition-all duration-300 py-2"
                  >
                    <Calendar className="w-4 h-4 mr-2" />
                    Book a Meeting
                  </Button> */}
                  </div>
                </form>

                {/* <div className="mt-8 pt-8 border-t border-border text-center">
                <p className="text-muted-foreground text-sm">
                  Prefer to email directly? Reach me at{" "}
                  <a
                    href="mailto:<EMAIL>"
                    className="text-primary hover:underline"
                  >
                    <EMAIL>
                  </a>
                </p>
              </div> */}
              </CardContent>
            </Card>
          </BlurFade>
        </div>
      </div>
    </section>
  );
};

function SubmitButton() {
  const { pending } = useFormStatus();

  return (
    <Button
      type="submit"
      size="lg"
      className="flex-1 bg-primary text-primary-foreground hover:bg-primary/90 transition-all duration-300 hover:shadow-lg hover:shadow-primary/25 py-2"
      disabled={pending}
    >
      {pending ? (
        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
      ) : (
        <Send className="w-4 h-4 mr-2" />
      )}
      {pending ? "Sending..." : "Send Message"}
    </Button>
  );
}
