"use client";

import { useTheme } from "next-themes";
import { ReactNode } from "react";
import { MagicCard } from "./magicui/magic-card";

export default function CardWithHoverLighting({
  children,
}: {
  children: ReactNode;
}) {
  const { theme } = useTheme();
  return (
    <MagicCard
      gradientColor={theme === "dark" ? "#262626" : "#D9D9D955"}
      className="cursor-pointer h-fit shadow-2xl rounded-lg group"
    >
      {children}
    </MagicCard>
  );
}
