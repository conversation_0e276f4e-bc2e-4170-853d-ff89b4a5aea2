
import type { LucideIcon } from "lucide-react";

export interface DevelopmentStep {
  id: number;
  icon: LucideIcon;
  title: string;
  description: string;
}

export interface ExpertiseCategory {
  category: string;
  icon: LucideIcon;
  technologies: string[];
  description: string;
}

export interface Employment {
  title: string;
  company: string;
  duration: string;
  description: string;
}

export interface EducationItem {
  institution: string;
  degree: string;
  duration: string;
  description: string;
}
