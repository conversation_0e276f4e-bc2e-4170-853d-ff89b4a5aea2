import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { employmentHistory, education } from "@/lib/constant";
import { Briefcase, GraduationCap } from "lucide-react";
import Card<PERSON>ithHoverLighting from "../CardWithHoverlighting";
import { BlurFade } from "../magicui/blur-fade";

export const ExperienceEducation = () => (
  <section id="about" className="bg-slate-900 py-20">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <BlurFade delay={0.25} inView>
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl text-white mb-4">
            Experience & Education
          </h2>
          <p className="text-lg text-gray-400 max-w-2xl mx-auto">
            A journey of continuous learning and professional growth in the
            world of technology.
          </p>
        </div>
      </BlurFade>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Employment History */}

        <div>
          <BlurFade delay={0.5} inView>
            <div className="flex items-center gap-3 mb-8">
              <div className="p-3 bg-blue-600/10 rounded-lg">
                <Briefcase className="w-6 h-6 text-blue-400" />
              </div>
              <h3 className="text-2xl text-white">Employment History</h3>
            </div>
          </BlurFade>

          <div className="space-y-6">
            {employmentHistory.map((job, index) => (
              <BlurFade key={index} delay={0.25 + index * 0.05} inView>
                <CardWithHoverLighting>
                  <Card className="bg-slate-800 border-slate-700 hover:bg-slate-700/50 transition-all duration-300">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-white text-lg">
                        {job.title}
                      </CardTitle>
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1">
                        <CardDescription className="text-blue-400">
                          {job.company}
                        </CardDescription>
                        <span className="text-gray-400 text-sm">
                          {job.duration}
                        </span>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-300 text-sm leading-relaxed">
                        {job.description}
                      </p>
                    </CardContent>
                  </Card>
                </CardWithHoverLighting>
              </BlurFade>
            ))}
          </div>
        </div>

        {/* Education */}

        <div>
          <BlurFade delay={0.25} inView>
            <div className="flex items-center gap-3 mb-8">
              <div className="p-3 bg-blue-600/10 rounded-lg">
                <GraduationCap className="w-6 h-6 text-blue-400" />
              </div>
              <h3 className="text-2xl text-white">Education</h3>
            </div>
          </BlurFade>

          <div className="space-y-6">
            {education.map((edu, index) => (
              <BlurFade key={index} delay={0.25 + index * 0.05} inView>
                <Card
                  key={index}
                  className="bg-slate-800 border-slate-700 hover:bg-slate-700/50 transition-all duration-300"
                >
                  <CardHeader className="pb-3">
                    <CardTitle className="text-white text-lg">
                      {edu.institution}
                    </CardTitle>
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1">
                      <CardDescription className="text-blue-400">
                        {edu.degree}
                      </CardDescription>
                      <span className="text-gray-400 text-sm">
                        {edu.duration}
                      </span>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-300 text-sm leading-relaxed">
                      {edu.description}
                    </p>
                  </CardContent>
                </Card>
              </BlurFade>
            ))}
          </div>
        </div>
      </div>

      {/* <div className="text-center mt-16">
        <p className="text-gray-400 mb-6">
          Want to learn more about my professional journey?
        </p>
        <Button
          size="lg"
          className="bg-blue-600 hover:bg-blue-500 text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/25"
        >
          Download Resume
        </Button>
      </div> */}
    </div>
  </section>
);
