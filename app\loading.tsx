"use client";

import { motion } from "framer-motion";
import { useEffect, useState } from "react";

export default function Loading() {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const stages = [
      { progress: 20, duration: 300 },
      { progress: 40, duration: 400 },
      { progress: 60, duration: 500 },
      { progress: 80, duration: 300 },
      { progress: 95, duration: 200 },
      { progress: 100, duration: 100 },
    ];

    let currentStageIndex = 0;

    const progressInterval = setInterval(() => {
      if (currentStageIndex < stages.length) {
        const stage = stages[currentStageIndex];
        setProgress(stage.progress);

        if (stage.progress === 100) {
          setTimeout(() => {
            clearInterval(progressInterval);
          }, 500);
        }

        currentStageIndex++;
      }
    }, stages[currentStageIndex]?.duration || 300);

    return () => clearInterval(progressInterval);
  }, []);

  useEffect(() => {
    const handleLoad = () => {
      setProgress(100);
    };

    const handleDOMContentLoaded = () => {
      if (progress < 80) {
        setProgress(80);
      }
    };

    if (document.readyState === "complete") {
      handleLoad();
    } else if (document.readyState === "interactive") {
      handleDOMContentLoaded();
    }

    window.addEventListener("load", handleLoad);
    document.addEventListener("DOMContentLoaded", handleDOMContentLoaded);

    return () => {
      window.removeEventListener("load", handleLoad);
      document.removeEventListener("DOMContentLoaded", handleDOMContentLoaded);
    };
  }, [progress]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="w-96 h-3 bg-gray-700 rounded-full overflow-hidden shadow-inner">
        <motion.div
          className="h-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-full"
          initial={{ width: "0%" }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 0.5, ease: "easeOut" }}
        />
      </div>
    </div>
  );
}
