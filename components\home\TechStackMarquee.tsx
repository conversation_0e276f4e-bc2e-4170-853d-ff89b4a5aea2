import { Marquee } from "@/components/magicui/marquee";
import { techStack } from "@/lib/constant";
import { cn } from "@/lib/utils";

const TechStackMarquee = () => {
  return (
    <div className="w-full overflow-x-hidden">
      <Marquee pauseOnHover className="[--duration:20s]">
        {techStack.map((tech) => (
          <div
            key={tech.name}
            className={cn(
              "flex h-full items-center justify-center rounded-md border border-slate-700 bg-slate-800 px-4 py-2 text-gray-300",
              "md:w-36"
            )}
          >
            <tech.icon className="mr-2 h-6 w-6" />
            <span className="text-sm font-medium">{tech.name}</span>
          </div>
        ))}
      </Marquee>
    </div>
  );
};

export default TechStackMarquee;
