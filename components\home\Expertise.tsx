import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { expertise } from "@/lib/constant";
import CardWithHoverLighting from "../CardWithHoverlighting";
import Link from "next/link";
import { BlurFade } from "../magicui/blur-fade";

export const Expertise = () => (
  <section id="skills" className="bg-muted/30 py-20">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <BlurFade delay={0.1} inView>
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl text-foreground mb-4">
            My Expertise
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            A comprehensive toolkit of modern technologies and platforms to
            bring your vision to life.
          </p>
        </div>
      </BlurFade>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {expertise.map((category, index) => {
          const IconComponent = category.icon;
          return (
            <BlurFade
              key={category.category}
              delay={0.25 + index * 0.05}
              inView
            >
              <CardWithHoverLighting>
                <Card className="bg-transparent border-none">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="p-2 bg-primary/5 rounded-lg group-hover:bg-primary/10 transition-colors duration-300">
                        <IconComponent className="w-5 h-5 text-primary" />
                      </div>
                      <CardTitle className="text-lg">
                        {category.category}
                      </CardTitle>
                    </div>
                    <CardDescription className="text-muted-foreground text-sm">
                      {category.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {category.technologies.map((tech) => (
                        <span
                          key={tech}
                          className="px-3 py-1 bg-muted text-muted-foreground rounded-md text-sm hover:bg-accent hover:text-accent-foreground transition-colors duration-200 cursor-default"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </CardWithHoverLighting>
            </BlurFade>
          );
        })}
      </div>

      <BlurFade delay={0.25 * 0.05} inView>
        <div className="text-center mt-16">
          <p className="text-muted-foreground mb-6">
            Looking for a specific technology not listed here?
          </p>
          <Button
            variant="outline"
            size="lg"
            className="border-border hover:bg-accent hover:text-accent-foreground transition-all duration-300"
          >
            <Link
              href="https://wa.me/923356478086"
              target="_blank"
              rel="noopener noreferrer"
            >
              Let's Talk About Your Needs
            </Link>
          </Button>
        </div>
      </BlurFade>
    </div>
  </section>
);
