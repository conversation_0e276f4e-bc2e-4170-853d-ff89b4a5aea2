import {
  DevelopmentStep,
  EducationItem,
  Employment,
  ExpertiseCategory,
} from "@/types/types";
import {
  Search,
  Code,
  Rocket,
  Monitor,
  Server,
  Cloud,
  Database,
  CreditCard,
} from "lucide-react";
import { FaReact, FaNodeJs, FaPython } from "react-icons/fa";
import {
  SiNextdotjs,
  SiTypescript,
  SiSass,
  SiDjango,
  SiFlask,
  SiFastapi,
} from "react-icons/si";


export const projects = [
  {
    id: 1,
    title: "Local Services Marketplace",
    description:
      "I've developed a full-stack local services marketplace connecting service providers with customers. The platform features user authentication with role-based access, service listings with multiple pricing options, image uploads via Cloudinary, and a booking system with scheduling capabilities. Built with React, TypeScript and Redux on the frontend, with MongoDB/Mongoose backend, the application showcases modern development practices including component architecture, state management with Redux, and form validation using react-hook-form with zod. The responsive UI utilizes shadcn components.",
    image: "/portfolio/local-connect.png",
    technologies: [
      "React",
      "Vite",
      "TypeScript",
      "Redux",
      "Node.js",
      "Stripe",
      "MongoDB",
      "Tailwind CSS",
    ],
    category: "SaaS",
    liveUrl: "https://full-stack-react-app-black.vercel.app/",
    githubUrl: "#",
  },
  {
    id: 2,
    title: "Home Renovation Services",
    description:
      "Biidit is a comprehensive platform designed to streamline the bidding process for global projects. The client-side application is built using React, Vite, and shadcn/ui, ensuring a responsive and user-friendly interface. On the server side, it employs Node.js with Express.js and MongoDB, providing a robust and scalable backend infrastructure. This combination of technologies facilitates efficient data management and real-time updates, enhancing the overall user experience. The platform's architecture allows for seamless integration of new features and ensures high performance.",
    image: "/portfolio/biidit.png",
    technologies: [
      "React",
      "TypeScript",
      "Mongodb",
      "Socket.io",
      "shadcnui",
      "Tailwind CSS",
      "AWS",
      "Google Maps API",
      "Nodejs",
      "Express.js",
      "Redux",
    ],
    category: "Full-Stack",
    liveUrl: "https://biidit.com/",
    githubUrl: "#",
  },
  {
    id: 3,
    title: "Marketsz",
    description:
      "We developed 'Market Z,' a dynamic e-commerce platform using Next.js and TypeScript. This project showcases a robust, scalable, and SEO-friendly web application, offering a seamless shopping experience.",
    image: "/portfolio/marketz.png",
    technologies: ["Next.js", "Shadcnui", "Chart.js", "Framer motion"],
    category: "Frontend",
    liveUrl: "https://market-z-ten.vercel.app/",
    githubUrl: "#",
  },
  {
    id: 4,
    title: "woo-commerce",
    description:
      "Introducing Woo Mart, a sophisticated e-commerce platform meticulously developed using Next.js and TypeScript. This project embodies the integration of modern web technologies to deliver a seamless, efficient, and user-friendly shopping experience",
    image: "/portfolio/woo-commerce.png",
    technologies: ["Next.js", "TypeScript", "shadcnui"],
    category: "Frontend",
    liveUrl: "https://woo-comerce.vercel.app/",
    githubUrl: "#",
  },
  {
    id: 5,
    title: "Venday - Healthcare Management",
    description:
      "Introducing Venday, a cutting-edge healthcare staffing platform designed to seamlessly connect care providers, hospitals, and staffing agencies. Built with Next.js and utilizing the shadcn/ui component library, Venday offers an intuitive and efficient solution for managing temporary healthcare staffing needs..",
    image: "/portfolio/venday.png",
    technologies: [
      "React",
      "Node.js",
      "Express.js",
      "MongoDB",
      "Socket.io",
      "AWS",
      "Encryption",
    ],
    category: "Full-Stack",
    liveUrl: "https://venday.co.uk/",
    githubUrl: "#",
  },
  {
    id: 5,
    title: "The Magnetismo",
    description:
      "The device utilizes magnetic energy to help manage migraines and relieve stress on the eyes during screen use, allowing users to regain a sense of mental calmnes.",
    image: "/portfolio/magnetismo.png",
    technologies: [
      "React",
      "Shopify",
      "Nodejs",
      "MongoDB",
      "Redux",
      "Shopify store front",
    ],
    category: "Full-Stack",
    liveUrl: "https://www.themagnetismo.com/",
    githubUrl: "#",
  },
  {
    id: 6,
    title: "Burstmode-AI",
    description:
      "Transform photos with Burst Mode's AI-powered tools. Using the Astria AI API, this platform enables users to create ultra-realistic, custom-styled images with advanced fine-tuning capabilities. Generate professional headshots, enhance food & product photography, and produce stunning visuals for social media and marketing. Saving time and money by training a personalized AI model, ensuring your products are consistently showcased in the best light.",
    image: "/portfolio/burstmodeAI.png",
    technologies: [
      "React",
      "Nodejs",
      "Nextjs",
      "HeroUi",
      "Framer Motion",
      "Firebase",
      "ci/cd",
      "Docker",
      "Google cloud run"
    ],
    category: "Full-Stack, AI Integration, SaaS",
    liveUrl: "https://www.burstmode.ai/",
    githubUrl: "#",
  },
];
export const techStack = [
  { name: "React", icon: FaReact },
  { name: "Next.js", icon: SiNextdotjs },
  { name: "Node.js", icon: FaNodeJs },
  { name: "TypeScript", icon: SiTypescript },
  { name: "Sass", icon: SiSass },
  { name: "Python", icon: FaPython },
  { name: "Django", icon: SiDjango },
  { name: "Flask", icon: SiFlask },
  { name: "FastApi", icon: SiFastapi },
];

export const developmentSteps: DevelopmentStep[] = [
  {
    id: 1,
    icon: Search,
    title: "Discovery & Strategy",
    description:
      "I begin by thoroughly understanding your business objectives, target audience, and technical requirements. Through detailed consultations and research, I develop a comprehensive project strategy that aligns with your goals and ensures optimal user experience.",
  },
  {
    id: 2,
    icon: Code,
    title: "Development & Integration",
    description:
      "Using agile methodologies, I build your solution with modern technologies like React, Next.js, and Node.js. I focus on clean, scalable code while integrating essential features, APIs, and third-party services to create a robust, high-performance application.",
  },
  {
    id: 3,
    icon: Rocket,
    title: "Deployment & Support",
    description:
      "After thorough testing and optimization, I deploy your application to production with proper monitoring and security measures. I provide ongoing maintenance, performance optimization, and technical support to ensure your solution continues to perform at its best.",
  },
];

export const expertise: ExpertiseCategory[] = [
  {
    category: "Front-End",
    icon: Monitor,
    technologies: [
      "React",
      "Next.js",
      "Remix",
      "TypeScript",
      "Tailwind CSS",
      "JavaScript",
    ],
    description: "Modern UI frameworks and languages",
  },
  {
    category: "Back-End",
    icon: Server,
    technologies: [
      "Node.js",
      "Python",
      "Django",
      "Express.js",
      "REST APIs",
      "GraphQL",
    ],
    description: "Server-side development and APIs",
  },
  {
    category: "DevOps",
    icon: Cloud,
    technologies: [
      "AWS",
      "Google Cloud",
      "Docker",
      "Vercel",
      "GitHub Actions",
      "CI/CD",
    ],
    description: "Cloud infrastructure and deployment",
  },
  {
    category: "Databases",
    icon: Database,
    technologies: [
      "MongoDB",
      "Supabase",
      "PostgreSQL",
      "Redis",
      "Prisma",
      "Firebase",
    ],
    description: "Data storage and management",
  },
  {
    category: "SaaS",
    icon: CreditCard,
    technologies: [
      "Stripe",
      "Social Login",
      "Auth0",
      "SendGrid",
      "Twilio",
      "Analytics",
    ],
    description: "Third-party integrations and services",
  },
];

export const employmentHistory: Employment[] = [
  {
    title: "Lead Full Stack Developer",
    company: "Tech Creator",
    duration: "March 2024 - Present",
    description:
      "Leading development of scalable web applications using React, Node.js, and cloud technologies. Managing a team of developers and architecting modern SaaS solutions.",
  },
  {
    title: "Senior Web Developer",
    company: "AbTech Software Solution",
    duration: "June 2022 - February 2024",
    description:
      "Developed and maintained complex web applications, implemented CI/CD pipelines, and collaborated with cross-functional teams to deliver high-quality software products.",
  },
];

export const education: EducationItem[] = [
  {
    institution: "University of Swabi",
    degree: "Bachelor of Science (BS), Computer Science",
    duration: "2019 - 2024",
    description:
      "Focused on software engineering, algorithms, and web development. Graduated with honors and completed several notable projects in full-stack development.",
  },
  {
    institution: "Khyber Pakhtunkhwa Board of Technical and Commerce Education",
    degree: "Engineer's degree, Computer engineering",
    duration: "2016-2019",
    description:
      " I completed a three-year diploma in computer hardware, gaining expertise in the physical components and architecture of computer systems. My studies provided me with a strong foundation in building, troubleshooting, and maintaining various hardware configurations, from individual components to complex network systems.",
  },
  {
    institution: "Khyber Pakhtunkhwa Board of Technical and Commerce Education",
    degree: "Diploma Of Informational Technology",
    duration: "2017-2018",
    description:
      "My one-year diploma in Information Technology provided me with a solid understanding of software development principles. I gained hands-on experience in database design, creating robust and efficient data structures, and learned how to implement effective software development models to ensure a seamless and organized process from project inception to completion",
  },
];
