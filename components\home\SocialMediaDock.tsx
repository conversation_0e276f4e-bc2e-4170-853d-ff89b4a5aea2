"use client";

import { Dock, DockIcon } from "@/components/magicui/dock";
import { <PERSON>edin } from "lucide-react";
import Link from "next/link";
import { SiUpwork, SiWhatsapp } from "react-icons/si";

export function SocialMediaDock() {
  return (
    <div className="fixed right-0 top-1/2 transform -translate-y-1/2 z-50">
      <Dock
        className="flex-col h-auto  gap-2 p-2 bg-white/10 dark:bg-black/10 backdrop-blur-md border border-white/20 dark:border-white/10"
        iconMagnification={60}
        iconDistance={100}
      >
        <DockIcon className="bg-blue-600 hover:bg-blue-700 transition-colors duration-200">
          <Link
            href="https://www.linkedin.com/in/naeem-khan-b205b8304"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center justify-center w-full h-full"
          >
            <Linkedin className="w-5 h-5 text-white" />
          </Link>
        </DockIcon>

        <DockIcon className="bg-green-600 hover:bg-green-700 transition-colors duration-200">
          <Link
            href="https://wa.me/923356478086"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center justify-center w-full h-full"
          >
            <SiWhatsapp className="w-5 h-5 text-white" />
          </Link>
        </DockIcon>

        <DockIcon className="bg-green-700 hover:bg-green-800 transition-colors duration-200">
          <Link
            href="https://www.upwork.com/freelancers/~012b38503956a57aee?mp_source=share"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center justify-center w-full h-full"
          >
            <SiUpwork className="w-5 h-5 text-white" />
          </Link>
        </DockIcon>
      </Dock>
    </div>
  );
}
