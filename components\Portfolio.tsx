"use client";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>it<PERSON>,
} from "./ui/card";
import { Badge } from "./ui/badge";
import { Button } from "./ui/button";
import { ExternalLink, Github } from "lucide-react";
import BlurImage from "./BlurImage";
import Card<PERSON><PERSON><PERSON>overLighting from "./CardWithHoverlighting";
import { projects } from "@/lib/constant";
import Link from "next/link";
import { BlurFade } from "./magicui/blur-fade";

export function Portfolio() {
  const categories = [
    "All",
    "Full-Stack",
    "SaaS",
    "Analytics",
    "Education",
    "Real Estate",
    "Healthcare",
  ];

  return (
    <section id="work" className="bg-background py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <BlurFade delay={0.25 * 0.05} inView>
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl text-foreground mb-4">
              My Work
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              A showcase of projects I've built using modern web technologies.
              Each project demonstrates different aspects of full-stack
              development.
            </p>
          </div>
        </BlurFade>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-2 mb-12">
          {categories.map((category, idx) => (
            <BlurFade key={idx} delay={0.25 + idx * 0.05} inView>
              <Button
                key={category}
                variant={category === "All" ? "default" : "outline"}
                size="sm"
                className="transition-all duration-200"
              >
                {category}
              </Button>
            </BlurFade>
          ))}
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, idx) => (
            <BlurFade key={idx} delay={0.25 + idx * 0.05} inView>
              <CardWithHoverLighting>
                <Card
                  key={project.id}
                  className="bg-transparent border-none p-0 pb-5 group"
                >
                  <div className="relative overflow-hidden">
                    <BlurImage src={project.image} alt={project.title} />
                    <div className="absolute inset-0 bg-black/50 opacity-0  group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-3 z-10">
                      <Link
                        href={project.liveUrl}
                        className="bg-white/90 text-black hover:bg-white flex items-center p-1 rounded shadow"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        Live Demo
                      </Link>
                      <Link
                        target="_blank"
                        rel="noopener noreferrer"
                        href={project.githubUrl}
                        className="bg-white/90 text-black hover:bg-white flex items-center p-1 rounded shadow"
                      >
                        <Github className="w-4 h-4 mr-2" />
                        Code
                      </Link>
                    </div>
                  </div>

                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="secondary" className="text-xs">
                        {project.category}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg group-hover:text-primary transition-colors duration-200">
                      {project.title}
                    </CardTitle>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <CardDescription className="text-muted-foreground leading-relaxed text-sm max-h-24 overflow-hidden">
                      {project.description}
                    </CardDescription>

                    <div className="flex flex-wrap gap-1.5">
                      {project.technologies.map((tech) => (
                        <Badge
                          key={tech}
                          variant="outline"
                          className="text-xs px-2 py-1 border-primary/20 text-primary hover:bg-primary/5 transition-colors duration-200"
                        >
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </CardWithHoverLighting>
            </BlurFade>
          ))}
        </div>
        <BlurFade delay={0.25 * 0.05} inView>
          <div className="text-center mt-16">
            <p className="text-muted-foreground mb-6">
              Interested in working together on your next project?
            </p>
            <Button
              size="lg"
              className="bg-primary text-primary-foreground hover:bg-primary/90 transition-all duration-300 hover:shadow-lg hover:shadow-primary/25"
            >
              Start a Project
            </Button>
          </div>
        </BlurFade>
      </div>
    </section>
  );
}
