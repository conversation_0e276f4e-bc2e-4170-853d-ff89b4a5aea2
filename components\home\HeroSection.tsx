import AnimatedHeading from "./AnimatedHeading";
import TechStackMarquee from "./TechStackMarquee";
import { RainbowButton } from "../magicui/rainbow-button";
import Link from "next/link";
import { BlurFade } from "../magicui/blur-fade";

export const HeroSection = () => (
  <main
    id="home"
    className="min-h-[calc(100vh-4rem)] bg-slate-900 flex items-center justify-center px-4 sm:px-6 lg:px-8 flex-col"
  >
    <div className="max-w-4xl mx-auto text-center space-y-8">
      {/* Main Headline */}
      <BlurFade delay={0.25} inView>
        <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl text-white leading-tight tracking-tight">
          Building High-Performance
          <br />
          <span className="text-blue-400">Web & Mobile Solutions</span>
        </h1>
      </BlurFade>

      {/* Sub-headline */}
      <div className="space-y-4">
        <BlurFade delay={0.5} inView>
          <AnimatedHeading />
        </BlurFade>
        {/* <img src="/logo.png" className="w-4" /> */}
        <BlurFade delay={0.75} inView>
          <p className="text-lg sm:text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
            Specializing in React, Next.js, Node.js, and SaaS platforms to
            deliver scalable, modern applications that drive business growth and
            user engagement.
          </p>
        </BlurFade>
      </div>

      {/* CTA Button */}
      <BlurFade delay={1} inView>
        <div className="pt-4">
          <RainbowButton size="lg" className="" variant="outline">
            <Link href="/#work">View My Work</Link>
          </RainbowButton>
        </div>
      </BlurFade>
    </div>
    <div className="w-full pt-8">
      <BlurFade delay={1.25} inView>
        <TechStackMarquee />
      </BlurFade>
    </div>
  </main>
);
