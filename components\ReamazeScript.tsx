import Script from "next/script";

const ReamazeScript = () => {
  return (
    <>
      <Script
        id="reamaze-config"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            var _support = _support || { 'ui': {}, 'user': {} };
            _support['account'] = 'f64afd03-7c2b-4502-a086-319448ab6c35';
            _support['ui']['contactMode'] = 'mixed';
            _support['ui']['enableKb'] = 'true';
            _support['ui']['mailbox'] = '********';
            _support['ui']['styles'] = {
              widgetColor: 'rgba(16, 162, 197, 1)',
              gradient: true,
            };
            _support['ui']['shoutboxFacesMode'] = '';
            _support['ui']['widget'] = {
              allowBotProcessing: 'false',
              slug: 'full-stack',
              label: {
                text: 'Let us know if you have any questions! 😊',
                mode: "notification",
                delay: 3,
                duration: 30,
                primary: '',
                sound: true,
              },
              position: 'bottom-right'
            };
            _support['apps'] = {
              recentConversations: {},
              faq: {"enabled":true}
            };
          `,
        }}
      />
      <Script id="reamaze-loader" src="https://cdn.reamaze.com/assets/reamaze-loader.js" strategy="afterInteractive" async />
    </>
  );
};

export default ReamazeScript;
