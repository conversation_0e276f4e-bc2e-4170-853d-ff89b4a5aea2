"use server";

import nodemailer from "nodemailer";

export async function sendEmail(_: any, formData: FormData) {
  const name = formData.get("name") as string;
  const email = formData.get("email") as string;
  const message = formData.get("message") as string;

  const transporter = nodemailer.createTransport({
    service: "gmail",
    auth: {
      type: "OAuth2",
      user: "<EMAIL>",
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      refreshToken: process.env.GOOGLE_REFRESH_TOKEN,
    },
  });

  try {
    await transporter.sendMail({
      from: "'Client'  <<EMAIL>>",
      to: "<EMAIL>",
      subject: `New message from ${name}`,
      text: message,
      html: `<p>You have a new message from ${name} (${email}):</p><p>${message}</p>`,
    });
    return { success: true };
  } catch (error) {
    console.error(error);
    return { success: false };
  }
}
